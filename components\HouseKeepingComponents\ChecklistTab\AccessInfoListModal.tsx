import React from 'react';
import { StyleService, Button } from '@ui-kitten/components';
import { Modal, View } from 'react-native';
import { HKExtendedJob } from '~/types';
import PropertyCard from '~components/PropertyCard';
import useTimezone from '~hooks/useTimezone';

type Props = {
  job: HKExtendedJob | null;
  opened: boolean;
  onClose: () => void;
};

const AccessInfoListModal = ({ job, opened, onClose }: Props) => {
  const { formatInTimeZone } = useTimezone();
  if (!job || !job.id) return null;

  const {
    id,
    property: {
      name,
      address,
      accessInformation,
      notes,
      deletedAt,
      isSuspended,
    },
  } = job;

  const deletedAtWithTimezone = deletedAt
    ? new Date(formatInTimeZone(deletedAt, 'PP p'))
    : undefined;

  return (
    <Modal visible={opened} onRequestClose={onClose}>
      <View style={styles.modal}>
        <PropertyCard
          id={id}
          name={name}
          address={address}
          accessInformation={accessInformation}
          notes={notes}
          deletedAt={deletedAtWithTimezone}
          isSuspended={isSuspended}
          showDetails
        />
        <Button size="large" onPress={onClose} style={styles.button}>
          Close
        </Button>
      </View>
    </Modal>
  );
};

export default AccessInfoListModal;

const styles = StyleService.create({
  modal: {
    paddingVertical: 42,
    backgroundColor: 'white',
    height: '100%',
  },
  button: {
    marginTop: 'auto',
  },
});
