/**
 * Learn more about deep linking with React Navigation
 * https://reactnavigation.org/docs/deep-linking
 * https://reactnavigation.org/docs/configuring-links
 */

export default {
  prefixes: [
    `${process.env.DEEP_LINK_PREFIX}/`,
    `${process.env.FRONTEND_BASE_URL}/`,
  ],
  config: {
    screens: {
      HousekeepingScreen: {
        path: '/Root/Housekeeping/HouseKeepingScreen',
        parse: {
          checklistToken: (checklistToken: string) => checklistToken,
        },
      },
    },
  },
};
