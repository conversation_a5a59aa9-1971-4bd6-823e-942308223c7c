import React, { ReactNode } from 'react';
import { FlatList } from 'react-native';
import {
  HKS<PERSON><PERSON>,
  HKChecklist,
  HKTask,
  HKEx<PERSON>Job,
  HKUser,
  HKJobSection,
} from '~types';
import ChecklistSection from '~components/HouseKeepingComponents/ChecklistTab/ChecklistSection';

type Props = {
  sections: HKSection[];
  checklist: HKChecklist;
  tasks: HKTask[];
  job?: HKExtendedJob | null;
  user?: HKUser | null;
  onCompleteSection?: (
    jobSection: Omit<HKJobSection, 'completedAt'>,
  ) => Promise<void>;
  activeSection?: HKSection | null;
  onlyAssigning?: boolean;
  setStickyHeader?: (header: ReactNode | null) => void;
};

const ChecklistSections = ({
  job,
  user,
  sections,
  checklist,
  tasks,
  onCompleteSection,
  activeSection,
  onlyAssigning,
  setStickyHeader,
}: Props) => (
  <FlatList
    data={sections}
    keyExtractor={item => item.id.toString()}
    style={{ overflow: 'visible' }}
    contentContainerStyle={{ gap: 16, paddingBottom: 4 }}
    renderItem={({ item }) => (
      <ChecklistSection
        job={job}
        user={user}
        section={item}
        checklist={checklist}
        tasks={tasks.filter(task => task.sectionId === item.id)}
        disabled={
          onlyAssigning ||
          user?.performing !== checklist ||
          item.id !== activeSection?.id
        }
        onComplete={onCompleteSection}
        setStickyHeader={setStickyHeader}
      />
    )}
  />
);

export default ChecklistSections;
