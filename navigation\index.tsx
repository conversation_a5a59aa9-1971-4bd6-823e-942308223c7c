import {
  Navigation<PERSON>ontainer,
  DefaultTheme,
  DarkTheme,
  useRoute,
} from '@react-navigation/native';
import {
  StackNavigationProp,
  createStackNavigator,
} from '@react-navigation/stack';
import * as React from 'react';
import { forwardRef, useContext } from 'react';
import { ColorSchemeName } from 'react-native';
import NotFoundScreen from '../screens/NotFoundScreen';
import { RootStackParamList } from '../types';
import BottomTabNavigator from './BottomTabNavigator';
import LinkingConfiguration from './LinkingConfiguration';
import SignIn from '~screens/SignIn';
import SignUp from '~screens/SignUp';
import AuthContext from '~context/AuthContext';
import SkillsAfterRegistration from '~screens/SkillsAfterRegistration';
import Support from '~screens/Support';
import Activate from '~screens/Activate';
import ForgotPassword from '~screens/ForgotPassword';
import ResetPassword from '~screens/ResetPassword';
import HouseKeepingScreen from '~screens/HouseKeeping';

type RootStackNavigationProp = StackNavigationProp<RootStackParamList>;
type RefType = React.RefObject<RootStackNavigationProp> | null;
type PropsType = { colorScheme: ColorSchemeName };

const Navigation = forwardRef<RefType, PropsType>((props, ref) => (
  <NavigationContainer
    linking={LinkingConfiguration}
    theme={props.colorScheme === 'dark' ? DarkTheme : DefaultTheme}
    ref={ref}
  >
    <RootNavigator />
  </NavigationContainer>
));

export default Navigation;

const Stack = createStackNavigator<RootStackParamList>();

const RootNavigator = () => {
  const { authToken } = useContext(AuthContext);

  if (authToken === null) {
    return null;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {authToken ? (
        <>
          <Stack.Screen name="Root" component={BottomTabNavigator} />
          <Stack.Screen
            name="NotFound"
            component={NotFoundScreen}
            options={{ title: 'Oops!' }}
          />
          <Stack.Screen
            name="SkillsAfterRegistrationScreen"
            component={SkillsAfterRegistration}
            options={{ headerTitle: 'Skills' }}
          />
          <Stack.Screen name="ActivateAccountScreen" component={Activate} />
        </>
      ) : (
        <>
          <Stack.Screen name="SignIn">
            {props => <SignIn {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignUp">
            {props => <SignUp {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ForgotPassword">
            {props => <ForgotPassword {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ResetPassword">
            {props => <ResetPassword {...props} />}
          </Stack.Screen>
        </>
      )}
      <Stack.Screen name="Support">
        {props => <Support {...props} />}
      </Stack.Screen>
      <Stack.Screen name="HousekeepingScreen">
        {props => (
          <HouseKeepingScreen
            {...props}
            route={{
              ...props.route,
              name: 'HouseKeepingScreen',
              params: {
                checklistToken: props.route.params?.checklistToken || '',
              },
            }}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  );
};
