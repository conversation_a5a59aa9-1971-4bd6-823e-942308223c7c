import React, { ReactNode } from 'react';
import { Card, StyleService, useStyleSheet } from '@ui-kitten/components';
import { View } from 'react-native';
import ChecklistSections from '~components/HouseKeepingComponents/ChecklistTab/ChecklistSections';
import StartChecklistButton from '~components/HouseKeepingComponents/ChecklistTab/StartChecklistButton';
import LinkForOtherPerson from '~components/HouseKeepingComponents/ChecklistTab/LinkForOtherPerson';
import {
  HKChecklist,
  HKExtendedJob,
  HKSection,
  HKTask,
  HKUser,
  HKJobSection,
} from '~/types';

type Props = {
  checklist: HKChecklist;
  job: HKExtendedJob;
  user: HKUser | null;
  tasks: HKTask[];
  sections: HKSection[];
  performing: HKChecklist | null;
  onStartChecklist: () => Promise<void>;
  onCompleteSection: (
    jobSection: Omit<HKJobSection, 'completedAt'>,
  ) => Promise<void>;
  isLeader: boolean;
  onlyAssigning?: boolean;
  activeSection: HKSection | null;
  setStickyHeader?: (header: ReactNode | null) => void;
};

const ChecklistTab = ({
  checklist,
  job,
  user,
  tasks,
  sections,
  performing,
  onStartChecklist,
  onCompleteSection,
  isLeader,
  onlyAssigning,
  activeSection,
  setStickyHeader,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  const checklistStarted = job.jobSections.some(
    ({ checklist: jobChecklist }) => jobChecklist === performing,
  );

  const shouldShowLinkForOtherPerson =
    onlyAssigning || (isLeader && !!performing && checklist !== performing);

  const shouldShowStartButton =
    checklist === performing && !checklistStarted && !onlyAssigning;

  const filteredTasks = tasks.filter(task => task[checklist]);

  return (
    <View style={styles.gapContainer}>
      {shouldShowLinkForOtherPerson && (
        <Card disabled style={styles.link}>
          <LinkForOtherPerson job={job} checklist={checklist} />
        </Card>
      )}

      {shouldShowStartButton && (
        <StartChecklistButton onStartChecklist={onStartChecklist} />
      )}

      <ChecklistSections
        job={job}
        user={user}
        tasks={filteredTasks}
        sections={sections}
        checklist={checklist}
        onCompleteSection={onCompleteSection}
        activeSection={activeSection}
        onlyAssigning={onlyAssigning}
        setStickyHeader={setStickyHeader}
      />
    </View>
  );
};

export default ChecklistTab;

const themedStyles = StyleService.create({
  link: {
    backgroundColor: 'color-info-transparent-100',
    paddingHorizontal: 0,
  },
  gapContainer: {
    gap: 16,
  },
});
