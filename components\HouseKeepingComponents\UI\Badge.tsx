import React, { ReactNode } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import {
  Text,
  StyleService,
  useStyleSheet,
  IconElement,
  IconProps,
} from '@ui-kitten/components';
import Group from '~components/HouseKeepingComponents/UI/Group';
import theme from '~/theme.json';

type Props = {
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  variant?: 'filled' | 'outline' | 'clear';
  status?: 'success' | 'info' | 'warning';
  size?: 'medium' | 'large';
  leftSection?: (props: IconProps) => IconElement;
  rightSection?: (props: IconProps) => IconElement;
};

const getVariantStyle = (
  variant: Props['variant'],
  status: Props['status'] = 'info',
) => {
  const statusColor = statusColors[status];

  if (variant === 'filled') {
    return { backgroundColor: statusColor };
  }
  if (variant === 'outline') {
    return {
      backgroundColor: 'transparent',
      borderColor: statusColor,
      borderWidth: 1,
    };
  }
  return {};
};

const Badge = ({
  children,
  style: propStyle,
  variant = 'filled',
  status = 'info',
  size = 'medium',
  leftSection: LeftSection,
  rightSection: RightSection,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Group
      style={[
        styles.badge,
        getVariantStyle(variant, status),
        paddings[size],
        propStyle,
      ]}
    >
      {LeftSection && <LeftSection width={24} fill="white" />}
      <Text category="s1" style={styles.text} numberOfLines={2}>
        {children}
      </Text>
      {RightSection && <RightSection width={24} fill="white" />}
    </Group>
  );
};

export default Badge;

const themedStyles = StyleService.create({
  badge: {
    gap: 6,
    flexWrap: 'nowrap',
    borderRadius: 9999,
    flexShrink: 1,
    alignSelf: 'flex-start',
  },
  text: {
    color: 'white',
    textTransform: 'uppercase',
    fontWeight: 'bold',
    fontSize: 13,
  },
});

const paddings = StyleService.create({
  medium: { paddingHorizontal: 10, paddingVertical: 2 },
  large: { paddingHorizontal: 12, paddingVertical: 2 },
});

const statusColors = {
  success: theme['color-success-600'],
  info: theme['color-info-500'],
  warning: theme['color-warning-500'],
};
