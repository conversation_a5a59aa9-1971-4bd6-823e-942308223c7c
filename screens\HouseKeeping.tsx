import React from 'react';
import { Text } from '@ui-kitten/components';
import { Platform } from 'react-native';
import { openURL } from 'expo-linking';
import { useMutation, useQueryClient } from 'react-query';
import { StackScreenProps } from '~node_modules/@react-navigation/stack/lib/typescript/src';
import HouseKeepingPage from '~components/HouseKeepingComponents/HouseKeepingPage';
import {
  HouseKeepingParamList,
  HKJob,
  HKJobSection,
  HKPerformType,
} from '~types';

import {
  whoAmI,
  pdfReportPath,
  getUserFromChecklistToken,
} from '~helpers/hk-helpers';
import useHouseKeepingApi from '~api/useHouseKeepingApi';
import useHKItems from '~queries/useHKItems';
import useHKSections from '~queries/useHKSections';
import useHKJob from '~queries/useHKJob';

type Props = StackScreenProps<HouseKeepingParamList, 'HouseKeepingScreen'>;

const HouseKeepingScreen = ({ route, navigation }: Props) => {
  const {
    performer,
    checklistToken,
    propertyId,
    taskAssignmentId,
    jobId = 0,
  } = route.params || {};

  const queryClient = useQueryClient();
  const { addJob, completeJobSection, startChecklist, fetchJob } =
    useHouseKeepingApi();
  const {
    job,
    isLoading: jobIsLoading,
    refetch: refetchHKJob,
  } = useHKJob({
    jobId,
    checklistToken,
  });
  const { items: tasks, isLoading: itemsIsLoading } = useHKItems(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );
  const { sections, isLoading: sectionsIsLoading } = useHKSections(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );

  const { mutateAsync: addJobMutate } = useMutation(addJob);

  const { mutateAsync: startChecklistMutate } = useMutation(startChecklist, {
    onSuccess: () => {
      queryClient.invalidateQueries(['job', jobId, checklistToken]);
    },
  });
  const { mutateAsync: updateJobSectionMutate } = useMutation(
    completeJobSection,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['job', jobId, checklistToken]);
      },
    },
  );

  const isLoading = jobIsLoading || itemsIsLoading || sectionsIsLoading;

  if (
    (!jobId && !checklistToken && (!propertyId || !taskAssignmentId)) ||
    (!isLoading && (!tasks || !sections))
  ) {
    return <Text style={{ color: 'red', padding: 30 }}>Access Denied.</Text>;
  }

  if (!isLoading && (!propertyId || !taskAssignmentId) && !job) {
    return (
      <Text style={{ color: 'red', padding: 30 }}>
        This housekeeping job has not been created for this property.
      </Text>
    );
  }

  const user = checklistToken
    ? getUserFromChecklistToken(job || null, checklistToken)
    : whoAmI(job || null, performer === 'helper');
  const { performType } = job || {};

  const handleSaveAssignRolesForm = async (values: HKJob) => {
    if ((!propertyId || !taskAssignmentId) && !job) return;
    const updatedJob = await addJobMutate({
      job: values,
      taskAssignmentId: taskAssignmentId || job!.taskAssignmentId,
      propertyId: propertyId || job!.propertyId,
    });

    if (!values.id) {
      queryClient.invalidateQueries(['task-assignments', taskAssignmentId]);
      navigation.setParams({
        propertyId: undefined,
        taskAssignmentId: undefined,
        jobId: updatedJob.id,
      });
      return;
    }

    if (checklistToken) {
      const fetchedJob = await fetchJob(updatedJob.id);
      const checklistTokenIndex =
        fetchedJob.leaderPerforms === 'all' ||
        (user?.role === 'leader' && fetchedJob.leaderPerforms === 'person1') ||
        (user?.role === 'helper' && fetchedJob.leaderPerforms === 'person2')
          ? 0
          : 1;
      const updatedChecklistToken =
        fetchedJob.checklistTokens[checklistTokenIndex].token;

      navigation.setParams({ checklistToken: updatedChecklistToken });
    } else {
      refetchHKJob();
    }
  };

  const handleClickPDFReport = () => {
    if (!job) return;
    const url = pdfReportPath(jobId || job.id);
    if (Platform.OS === 'web') {
      window.open(url, '_blank');
    } else {
      openURL(url);
    }
  };

  const handleStartChecklist = async () => {
    if (!job || !user?.performing) return;
    await startChecklistMutate({
      jobId: jobId || job.id,
      checklist: user.performing,
    });
  };

  const handleCompleteSection = async (
    jobSection: Omit<HKJobSection, 'completedAt'>,
  ) => {
    if (!jobSection) return;
    await updateJobSectionMutate({ jobSection, checklistToken });
  };

  const handleClickFinish = async () => {
    if (!job!.taskAssignmentId) return;
    navigation.navigate('FinishTaskScreen', {
      taskAssignmentId: job!.taskAssignmentId,
    });
  };

  return (
    <HouseKeepingPage
      job={job || null}
      user={user}
      isLoading={isLoading}
      sections={sections || []}
      onStartChecklist={handleStartChecklist}
      onCompleteSection={handleCompleteSection}
      tasks={tasks || []}
      onClickFinish={handleClickFinish}
      onClickPDFReport={handleClickPDFReport}
      pdfReportPath={pdfReportPath(jobId || job?.id || 0)}
      onSaveAssignRolesForm={handleSaveAssignRolesForm}
      updateJob={refetchHKJob}
      canEditRoles
      onlyAssigning={performType === HKPerformType.Assigned}
    />
  );
};

export default HouseKeepingScreen;
