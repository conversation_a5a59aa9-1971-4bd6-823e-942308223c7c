import React, { ReactNode } from 'react';
import { View, ScrollView, ViewStyle, useWindowDimensions } from 'react-native';
import {
  Button,
  Modal,
  Text,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import Group from '~components/HouseKeepingComponents/UI/Group';
import { CloseIcon } from '~components/Icon';

type Props = {
  visible: boolean;
  title?: string;
  children: ReactNode;
  onClose?: () => void;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
};

const StickyHeaderModal = ({
  visible,
  onClose,
  title,
  children,
  style,
  contentStyle,
  ...modalProps
}: Props) => {
  const { height } = useWindowDimensions();
  const styles = useStyleSheet(themedStyles);

  return (
    <Modal
      visible={visible}
      style={[styles.modal, style]}
      backdropStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
      onBackdropPress={onClose}
      {...modalProps}
    >
      <View style={styles.container}>
        <View style={[styles.content, contentStyle]}>
          {(title || onClose) && (
            <Group style={styles.header}>
              <Text category="s1" style={styles.title}>
                {title}
              </Text>
              {onClose && (
                <Button
                  appearance="ghost"
                  status="basic"
                  onPress={onClose}
                  style={styles.closeButton}
                  accessoryLeft={CloseIcon}
                />
              )}
            </Group>
          )}
          <ScrollView
            style={{ maxHeight: height * (title ? 0.8 : 1.0) }}
            contentContainerStyle={{ flexGrow: 1 }}
          >
            {children}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const themedStyles = StyleService.create({
  modal: {
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    width: '90%',
  },
  header: {
    justifyContent: 'space-between',
    paddingBottom: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 18,
  },
  closeButton: {
    minWidth: 0,
    minHeight: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
    width: 28,
    height: 28,
  },
});

export default StickyHeaderModal;
