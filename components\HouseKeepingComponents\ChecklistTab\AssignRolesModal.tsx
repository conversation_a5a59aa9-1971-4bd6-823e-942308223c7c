import React from 'react';
import { HKJob } from '~/types';
import AssignRolesForm from '~/components/HouseKeepingComponents/AssignRolesForm/AssignRolesForm';
import StickyHeaderModal from '~components/HouseKeepingComponents/Shared/StickyHeaderModal';

type Props = {
  values: HKJob | null;
  opened: boolean;
  onClose: () => void;
  onSave: (values: HKJob) => Promise<void>;
};

const AssignRolesModal = ({ values, opened, onClose, onSave }: Props) => (
  <StickyHeaderModal visible={opened} onClose={onClose} title="Assign Roles">
    <AssignRolesForm values={values} onSave={onSave} />
  </StickyHeaderModal>
);

export default AssignRolesModal;
